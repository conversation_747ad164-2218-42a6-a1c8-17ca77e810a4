<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Factura
{
    // --- Atributos ---
    private ?int    $id           = null;
    private ?int    $id_proveedor = null;
    private ?string $fecha        = null;
    private ?float  $valor_total  = null;
    private ?int    $estado       = null;

    /**
     * Constructor: Inicializa las propiedades del objeto Factura.
     */
    public function __construct()
    {
        $this->id           = 0;
        $this->id_proveedor = null;
        $this->fecha        = null;
        $this->valor_total  = 0.0; // Default to 0 as per lunex patterns
        $this->estado       = 1; // Estado activo por defecto
    }

    /**
     * Método estático para construir un objeto Factura desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos de la factura.
     *
     * @return self Instancia de Factura.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                = new self();
            $objeto->id            = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->id_proveedor  = isset($resultado['id_proveedor']) ? (int)$resultado['id_proveedor'] : null;
            $objeto->fecha         = $resultado['fecha'] ?? null;
            $objeto->valor_total   = isset($resultado['valor_total']) ? (float)$resultado['valor_total'] : 0.0;
            $objeto->estado        = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir Factura: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una factura por su ID.
     *
     * @param int $id       ID de la factura.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto Factura o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM facturas
            WHERE
                id = :factura_id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":factura_id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener Factura (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de facturas.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos Factura.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                f.*,
                p.nombre as proveedor_nombre
            FROM facturas f
            LEFT JOIN proveedores p ON f.id_proveedor = p.id
            WHERE
                f.estado = 1
            ORDER BY
                f.fecha DESC, f.id DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de Facturas: " . $e->getMessage());
        }
    }

    /**
     * Obtiene facturas por proveedor.
     *
     * @param int $id_proveedor ID del proveedor.
     * @param PDO $conexion     Conexión PDO.
     *
     * @return array Array de objetos Factura.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_proveedor(int $id_proveedor, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM facturas
            WHERE
                id_proveedor = :proveedor_id
            ORDER BY
                fecha DESC, id DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":proveedor_id", $id_proveedor, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener facturas por proveedor (ID: $id_proveedor): " . $e->getMessage());
        }
    }

    /**
     * Crea una nueva factura en la base de datos a partir de un objeto Factura.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID de la nueva factura creada o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        if ($this->getId_proveedor() === null || $this->getId_proveedor() <= 0) {
            throw new Exception("ID de proveedor es requerido en el objeto Factura para crearlo.");
        }

        if (empty($this->getFecha())) {
            throw new Exception("Fecha es requerida en el objeto Factura para crearlo.");
        }

        try {
            $query = <<<SQL
            INSERT INTO facturas (
                 id_proveedor
                ,fecha
                ,valor_total
            ) VALUES (
                 :id_proveedor
                ,:fecha
                ,:valor_total
            )
            SQL;

            $statement = $conexion->prepare($query);

            $statement->bindValue(':id_proveedor', $this->getId_proveedor(), PDO::PARAM_INT);
            $statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);

            $success = $statement->execute();

            if ($success) {
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }

        } catch (PDOException $e) {
            if ($e->getCode() == 23000 || $e->getCode() == 1062) {
                throw new Exception("Error al crear factura: Violación de constraint de integridad.");
            } else {
                throw new Exception("Error de base de datos al crear factura: " . $e->getMessage());
            }
        } catch (Exception $e) {
            throw new Exception("Error al crear factura: " . $e->getMessage());
        }
    }

    /**
     * Modifica una factura existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    public function modificar(PDO $conexion): bool
    {
        if ($this->getId() === null || $this->getId() <= 0) {
            throw new Exception("ID válido es requerido en el objeto Factura para modificarlo.");
        }

        if ($this->getId_proveedor() === null || $this->getId_proveedor() <= 0) {
            throw new Exception("ID de proveedor es requerido en el objeto Factura para modificarlo.");
        }

        if (empty($this->getFecha())) {
            throw new Exception("Fecha es requerida en el objeto Factura para modificarlo.");
        }

        try {
            $query = <<<SQL
            UPDATE facturas SET
                id_proveedor = :id_proveedor,
                fecha = :fecha,
                valor_total = :valor_total
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_proveedor', $this->getId_proveedor(), PDO::PARAM_INT);
            $statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar factura (ID: " . $this->getId() . "): " . $e->getMessage());
        }
    }

    /**
     * Elimina una factura por su ID.
     *
     * @param int $id       ID de la factura a eliminar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            DELETE FROM facturas
            WHERE
                id = :factura_id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':factura_id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar factura (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Desactiva una factura estableciendo su estado a 0.
     *
     * @param int $id       ID de la factura a desactivar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la desactivación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            UPDATE facturas SET
                estado = 0
            WHERE
                id = :factura_id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':factura_id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al desactivar factura (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId_proveedor(): ?int
    {
        return $this->id_proveedor;
    }

    public function setId_proveedor(?int $id_proveedor): self
    {
        $this->id_proveedor = $id_proveedor;
        return $this;
    }

    public function getFecha(): ?string
    {
        return $this->fecha;
    }

    public function setFecha(?string $fecha): self
    {
        $this->fecha = $fecha;
        return $this;
    }

    public function getValor_total(): ?float
    {
        return $this->valor_total;
    }

    public function setValor_total(?float $valor_total): self
    {
        $this->valor_total = $valor_total ?? 0.0;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        if ($estado !== null && !in_array($estado, [0, 1])) {
            throw new \InvalidArgumentException("El estado debe ser 0, 1 o null.");
        }
        $this->estado = $estado;
        return $this;
    }

    // --- Métodos de relación ---

    /**
     * Obtiene el objeto Proveedor asociado a esta factura.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return Proveedor|null Objeto Proveedor o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public function getProveedor(PDO $conexion): ?Proveedor
    {
        if ($this->id_proveedor === null) {
            return null;
        }

        return Proveedor::get($this->id_proveedor, $conexion);
    }

    /**
     * Obtiene todos los detalles de esta factura.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos FacturaDetalle.
     * @throws Exception Si hay error en DB.
     */
    public function getDetalles(PDO $conexion): array
    {
        if ($this->id === null) {
            return [];
        }

        return FacturaDetalle::get_by_factura($this->id, $conexion);
    }

    /**
     * Obtiene todos los soportes de esta factura.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos FacturaSoporte.
     * @throws Exception Si hay error en DB.
     */
    public function getSoportes(PDO $conexion): array
    {
        if ($this->id === null) {
            return [];
        }

        return FacturaSoporte::get_by_factura($this->id, $conexion);
    }

    /**
     * Calcula el valor total de la factura basado en sus detalles.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return float Valor total calculado.
     * @throws Exception Si hay error en DB.
     */
    public function calcularValorTotal(PDO $conexion): float
    {
        $detalles = $this->getDetalles($conexion);
        $total = 0.0;

        foreach ($detalles as $detalle) {
            $total += $detalle->getValor_total() ?? 0.0;
        }

        return $total;
    }

    /**
     * Actualiza el valor total de la factura basado en sus detalles.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa.
     * @throws Exception Si hay error en DB.
     */
    public function actualizarValorTotal(PDO $conexion): bool
    {
        $nuevoTotal = $this->calcularValorTotal($conexion);
        $this->setValor_total($nuevoTotal);
        return $this->modificar($conexion);
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si la factura está activa.
     * @return bool
     */
    public function isActiva(): bool
    {
        return $this->estado === 1;
    }
}
