<?php
#region region DOCS

/** @var Factura[] $facturas */
/** @var Proveedor[] $proveedores */
/** @var bool $showResults */
/** @var array $filtros */

use App\classes\Factura;
use App\classes\Proveedor;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Listado de Facturas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="Gestión de facturas del sistema" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
    <link href="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- BEGIN #header -->
    <?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>
    <!-- END #header -->

    <!-- BEGIN #sidebar -->
    <?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>
    <!-- END #sidebar -->

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Listado de Facturas</h4>
                <p class="mb-0 text-muted">Gestiona las facturas del sistema con filtros avanzados</p>
            </div>
            <div class="ms-auto">
                <a href="ingresar-factura" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nueva</a>
            </div>
        </div>
        <hr>
        <?php #endregion PAGE HEADER ?>

        <?php #region region FLASH MESSAGES ?>
        <?php if (isset($_SESSION['flash_message_success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['flash_message_success']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['flash_message_success']); ?>
        <?php endif; ?>
        <?php if (isset($_SESSION['flash_message_error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['flash_message_error']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['flash_message_error']); ?>
        <?php endif; ?>
        <?php #endregion FLASH MESSAGES ?>

        <?php #region region FILTERS PANEL ?>
        <div class="panel panel-inverse no-border-radious mb-3">
            <div class="panel-heading no-border-radious">
                <h4 class="panel-title">Filtros de Búsqueda</h4>
                <div class="panel-heading-btn">
                    <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                    <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                </div>
            </div>
            <div class="panel-body">
                <form method="GET" action="listado-facturas" id="filtros-form">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="id_factura" class="form-label">ID Factura</label>
                            <input type="number" class="form-control" id="id_factura" name="id_factura" 
                                   value="<?php echo htmlspecialchars($filtros['id_factura']); ?>" 
                                   placeholder="Ej: 123">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="id_proveedor" class="form-label">Proveedor</label>
                            <select class="form-control" id="id_proveedor" name="id_proveedor">
                                <option value="">Todos los proveedores</option>
                                <?php foreach ($proveedores as $proveedor): ?>
                                    <option value="<?php echo $proveedor->getId(); ?>" 
                                            <?php echo ($filtros['id_proveedor'] == $proveedor->getId()) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($proveedor->getNombre()); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="fecha_desde" class="form-label">Fecha Desde</label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha_desde" name="fecha_desde" 
                                       value="<?php echo htmlspecialchars($filtros['fecha_desde']); ?>" 
                                       autocomplete="off" placeholder="yyyy-mm-dd">
                                <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="fecha_hasta" class="form-label">Fecha Hasta</label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha_hasta" name="fecha_hasta" 
                                       value="<?php echo htmlspecialchars($filtros['fecha_hasta']); ?>" 
                                       autocomplete="off" placeholder="yyyy-mm-dd">
                                <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                            </div>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" name="aplicar_filtros" class="btn btn-primary">
                            <i class="fa fa-search fa-fw me-1"></i> Aplicar Filtros
                        </button>
                        <a href="listado-facturas" class="btn btn-secondary">
                            <i class="fa fa-times fa-fw me-1"></i> Limpiar
                        </a>
                    </div>
                </form>
            </div>
        </div>
        <?php #endregion FILTERS PANEL ?>

        <?php #region region RESULTS PANEL ?>
        <?php if ($showResults): ?>
        <div class="panel panel-inverse no-border-radious">
            <div class="panel-heading no-border-radious">
                <h4 class="panel-title">
                    Resultados de la Búsqueda (<?php echo count($facturas); ?> facturas encontradas)
                </h4>
            </div>
            <div class="p-1 table-nowrap" style="overflow: auto">
                <?php if (empty($facturas)): ?>
                    <div class="alert alert-info text-center">
                        <i class="fa fa-info-circle fa-2x mb-2"></i>
                        <p class="mb-0">No se encontraron facturas que coincidan con los filtros aplicados.</p>
                    </div>
                <?php else: ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th>Acciones</th>
                            <th>ID</th>
                            <th>Proveedor</th>
                            <th class="text-center">Fecha</th>
                            <th class="text-end">Valor Total</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px" id="facturas-table-body">
                        <?php foreach ($facturas as $factura): ?>
                            <?php $proveedor = $factura->getProveedor($conexion); ?>
                            <tr data-factura-id="<?php echo $factura->getId(); ?>">
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-xs btn-primary btn-edit-fecha"
                                                title="Editar Fecha"
                                                data-factura-id="<?php echo $factura->getId(); ?>"
                                                data-fecha-actual="<?php echo htmlspecialchars($factura->getFecha()); ?>">
                                            <i class="fa fa-calendar-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-info btn-view-detalles"
                                                title="Ver Detalles"
                                                data-factura-id="<?php echo $factura->getId(); ?>">
                                            <i class="fa fa-list"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-warning btn-view-soportes"
                                                title="Ver Soportes"
                                                data-factura-id="<?php echo $factura->getId(); ?>">
                                            <i class="fa fa-file-pdf"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-danger btn-soft-delete"
                                                title="Eliminar"
                                                data-factura-id="<?php echo $factura->getId(); ?>"
                                                data-proveedor="<?php echo htmlspecialchars($proveedor ? $proveedor->getNombre() : 'N/A'); ?>">
                                            <i class="fa fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                                <td><?php echo $factura->getId(); ?></td>
                                <td><?php echo htmlspecialchars($proveedor ? $proveedor->getNombre() : 'N/A'); ?></td>
                                <td class="text-center"><?php echo htmlspecialchars($factura->getFecha()); ?></td>
                                <td class="text-end">
                                    $<?php echo number_format($factura->getValor_total() ?? 0, 0, ',', '.'); ?> COP
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
        <?php else: ?>
        <div class="panel panel-inverse no-border-radious">
            <div class="panel-body text-center">
                <i class="fa fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Aplique filtros para ver las facturas</h5>
                <p class="text-muted">Use los filtros de arriba para buscar facturas específicas.</p>
            </div>
        </div>
        <?php endif; ?>
        <?php #endregion RESULTS PANEL ?>

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region MODALS ?>

<!-- Edit Date Modal -->
<div class="modal fade" id="editFechaModal" tabindex="-1" aria-labelledby="editFechaModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editFechaModalLabel">Editar Fecha de Factura</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="edit-fecha-form">
                    <input type="hidden" id="edit-factura-id" name="factura_id">
                    <div class="mb-3">
                        <label for="nueva-fecha" class="form-label">Nueva Fecha <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control datepicker" id="nueva-fecha" name="nueva_fecha" required autocomplete="off">
                            <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="save-fecha-btn">Guardar Cambios</button>
            </div>
        </div>
    </div>
</div>

<!-- View Details Modal -->
<div class="modal fade" id="viewDetallesModal" tabindex="-1" aria-labelledby="viewDetallesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewDetallesModalLabel">Detalles de la Factura</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="detalles-content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- View Support Documents Modal -->
<div class="modal fade" id="viewSoportesModal" tabindex="-1" aria-labelledby="viewSoportesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewSoportesModalLabel">Documentos de Soporte</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="soportes-content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<?php #endregion MODALS ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
<script src="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA_RESOURCES ?>js/datepickerini.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tableBody = document.getElementById('facturas-table-body');

    // Modal elements
    const editFechaModal = new bootstrap.Modal(document.getElementById('editFechaModal'));
    const viewDetallesModal = new bootstrap.Modal(document.getElementById('viewDetallesModal'));
    const viewSoportesModal = new bootstrap.Modal(document.getElementById('viewSoportesModal'));

    if (tableBody) {
        tableBody.addEventListener('click', function(event) {
            const editFechaBtn = event.target.closest('.btn-edit-fecha');
            const viewDetallesBtn = event.target.closest('.btn-view-detalles');
            const viewSoportesBtn = event.target.closest('.btn-view-soportes');
            const softDeleteBtn = event.target.closest('.btn-soft-delete');

            // Handle Edit Date
            if (editFechaBtn) {
                event.preventDefault();
                const facturaId = editFechaBtn.dataset.facturaId;
                const fechaActual = editFechaBtn.dataset.fechaActual;

                document.getElementById('edit-factura-id').value = facturaId;
                document.getElementById('nueva-fecha').value = fechaActual;
                editFechaModal.show();
            }

            // Handle View Details
            if (viewDetallesBtn) {
                event.preventDefault();
                const facturaId = viewDetallesBtn.dataset.facturaId;
                loadFacturaDetalles(facturaId);
                viewDetallesModal.show();
            }

            // Handle View Support Documents
            if (viewSoportesBtn) {
                event.preventDefault();
                const facturaId = viewSoportesBtn.dataset.facturaId;
                loadFacturaSoportes(facturaId);
                viewSoportesModal.show();
            }

            // Handle Soft Delete
            if (softDeleteBtn) {
                event.preventDefault();
                const facturaId = softDeleteBtn.dataset.facturaId;
                const proveedor = softDeleteBtn.dataset.proveedor;

                Swal.fire({
                    title: '¿Está seguro?',
                    text: `¿Desea eliminar la factura del proveedor "${proveedor}"?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Sí, eliminar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        softDeleteFactura(facturaId);
                    }
                });
            }
        });
    }

    // Save Date Changes
    document.getElementById('save-fecha-btn').addEventListener('click', function() {
        const form = document.getElementById('edit-fecha-form');
        const formData = new FormData(form);
        formData.append('action', 'edit_fecha');

        fetch('listado-facturas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                editFechaModal.hide();
                // Update the date in the table
                const facturaId = document.getElementById('edit-factura-id').value;
                const row = document.querySelector(`tr[data-factura-id="${facturaId}"]`);
                if (row) {
                    const fechaCell = row.querySelector('td:nth-child(4)');
                    if (fechaCell) {
                        fechaCell.textContent = data.nueva_fecha;
                    }
                    // Update the button data attribute
                    const editBtn = row.querySelector('.btn-edit-fecha');
                    if (editBtn) {
                        editBtn.dataset.fechaActual = data.nueva_fecha;
                    }
                }

                // Show success toast
                Swal.fire({
                    icon: 'success',
                    title: 'Fecha actualizada',
                    text: data.message,
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: data.message
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Error de comunicación al actualizar la fecha.'
            });
        });
    });

    // Load Factura Details
    function loadFacturaDetalles(facturaId) {
        const content = document.getElementById('detalles-content');
        content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Cargando...</span></div></div>';

        const formData = new FormData();
        formData.append('action', 'get_detalles');
        formData.append('factura_id', facturaId);

        fetch('listado-facturas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = `
                    <div class="row mb-3">
                        <div class="col-md-6"><strong>ID Factura:</strong> ${data.factura.id}</div>
                        <div class="col-md-6"><strong>Proveedor:</strong> ${data.factura.proveedor_nombre}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6"><strong>Fecha:</strong> ${data.factura.fecha}</div>
                        <div class="col-md-6"><strong>Valor Total:</strong> $${new Intl.NumberFormat('es-CO').format(data.factura.valor_total || 0)} COP</div>
                    </div>
                    <h6>Detalles de la Factura:</h6>
                `;

                if (data.detalles.length > 0) {
                    html += `
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>Descripción</th>
                                        <th class="text-center">Cantidad</th>
                                        <th class="text-end">Valor Unitario</th>
                                        <th class="text-end">Valor Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;

                    data.detalles.forEach(detalle => {
                        html += `
                            <tr>
                                <td>${detalle.descripcion}</td>
                                <td class="text-center">${detalle.cantidad}</td>
                                <td class="text-end">$${new Intl.NumberFormat('es-CO').format(detalle.valor_unitario || 0)}</td>
                                <td class="text-end">$${new Intl.NumberFormat('es-CO').format(detalle.valor_total || 0)}</td>
                            </tr>
                        `;
                    });

                    html += `
                                </tbody>
                            </table>
                        </div>
                    `;
                } else {
                    html += '<div class="alert alert-info">No hay detalles registrados para esta factura.</div>';
                }

                content.innerHTML = html;
            } else {
                content.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = '<div class="alert alert-danger">Error al cargar los detalles de la factura.</div>';
        });
    }

    // Load Factura Support Documents
    function loadFacturaSoportes(facturaId) {
        const content = document.getElementById('soportes-content');
        content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Cargando...</span></div></div>';

        const formData = new FormData();
        formData.append('action', 'get_soportes');
        formData.append('factura_id', facturaId);

        fetch('listado-facturas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<h6>Documentos de Soporte:</h6>';

                if (data.soportes.length > 0) {
                    html += '<div class="list-group">';

                    data.soportes.forEach(soporte => {
                        html += `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fa fa-file-pdf text-danger me-2"></i>
                                    ${soporte.nombre_archivo}
                                </div>
                                <a href="resources/uploads/facturas/${facturaId}/${soporte.nombre_archivo}"
                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fa fa-download"></i> Descargar
                                </a>
                            </div>
                        `;
                    });

                    html += '</div>';
                } else {
                    html += '<div class="alert alert-info">No hay documentos de soporte para esta factura.</div>';
                }

                content.innerHTML = html;
            } else {
                content.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = '<div class="alert alert-danger">Error al cargar los documentos de soporte.</div>';
        });
    }

    // Soft Delete Factura
    function softDeleteFactura(facturaId) {
        const formData = new FormData();
        formData.append('action', 'soft_delete');
        formData.append('factura_id', facturaId);

        fetch('listado-facturas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the row from the table
                const row = document.querySelector(`tr[data-factura-id="${facturaId}"]`);
                if (row) {
                    row.remove();
                }

                // Show success toast
                Swal.fire({
                    icon: 'success',
                    title: 'Factura eliminada',
                    text: data.message,
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000
                });

                // Check if table is now empty
                const tbody = document.getElementById('facturas-table-body');
                if (tbody && tbody.children.length === 0) {
                    location.reload(); // Reload to show "no results" message
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: data.message
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Error de comunicación al eliminar la factura.'
            });
        });
    }
});
</script>
<?php #endregion JS ?>
</body>
</html>
