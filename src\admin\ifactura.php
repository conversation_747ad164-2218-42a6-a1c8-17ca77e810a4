<?php
#region region DOCS
/**
 * <PERSON><PERSON>r Nueva Factura - Backend Controller
 * 
 * Handles the invoice creation page with header, details, and support documents.
 * Creates Factura, FacturaDetalle, and FacturaSoporte records.
 */

use App\classes\Factura;
use App\classes\Proveedor;
use App\classes\FacturaDetalle;
use App\classes\FacturaSoporte;

#endregion DOCS

require_once __DIR__ . '/../../config/init.php';

// Verificar autenticación
if (!isset($_SESSION['usuario_id'])) {
    header('Location: ' . RUTA . 'admin/login');
    exit;
}

try {
    // Initialize variables
    $proveedores = [];
    $errors = [];
    $formData = [
        'id_proveedor' => '',
        'fecha' => '',
        'detalles' => [],
        'archivos' => []
    ];

    // Get all active providers for the dropdown
    $proveedores = Proveedor::get_list($conexion);

    #region region Handle form submission
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // Get form data
        $id_proveedor = filter_input(INPUT_POST, 'id_proveedor', FILTER_VALIDATE_INT);
        $fecha = filter_input(INPUT_POST, 'fecha', FILTER_SANITIZE_SPECIAL_CHARS);
        
        // Get details data (JSON)
        $detalles_json = filter_input(INPUT_POST, 'detalles_data', FILTER_UNSAFE_RAW);
        $detalles_data = json_decode($detalles_json, true);

        // Validate required fields
        if (!$id_proveedor) {
            $errors[] = 'El proveedor es requerido.';
        }
        if (empty($fecha)) {
            $errors[] = 'La fecha es requerida.';
        }
        if (empty($detalles_data) || !is_array($detalles_data)) {
            $errors[] = 'Debe agregar al menos un detalle a la factura.';
        }

        // Validate details
        if (!empty($detalles_data)) {
            foreach ($detalles_data as $index => $detalle) {
                if (empty($detalle['descripcion'])) {
                    $errors[] = "La descripción del detalle " . ($index + 1) . " es requerida.";
                }
                if (!isset($detalle['cantidad']) || $detalle['cantidad'] <= 0) {
                    $errors[] = "La cantidad del detalle " . ($index + 1) . " debe ser mayor que cero.";
                }
                if (!isset($detalle['valor_unitario']) || $detalle['valor_unitario'] <= 0) {
                    $errors[] = "El valor unitario del detalle " . ($index + 1) . " debe ser mayor que cero.";
                }
            }
        }

        // Validate uploaded files (PDFs only)
        $uploaded_files = [];
        if (isset($_FILES['archivos']) && !empty($_FILES['archivos']['name'][0])) {
            for ($i = 0; $i < count($_FILES['archivos']['name']); $i++) {
                if ($_FILES['archivos']['error'][$i] === UPLOAD_ERR_OK) {
                    $file_name = $_FILES['archivos']['name'][$i];
                    $file_tmp = $_FILES['archivos']['tmp_name'][$i];
                    $file_size = $_FILES['archivos']['size'][$i];
                    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

                    // Validate file extension
                    if ($file_ext !== 'pdf') {
                        $errors[] = "El archivo '$file_name' debe ser un PDF.";
                        continue;
                    }

                    // Validate file size (max 10MB)
                    if ($file_size > 10 * 1024 * 1024) {
                        $errors[] = "El archivo '$file_name' es demasiado grande (máximo 10MB).";
                        continue;
                    }

                    $uploaded_files[] = [
                        'name' => $file_name,
                        'tmp_name' => $file_tmp,
                        'size' => $file_size
                    ];
                }
            }
        }

        // If no errors, proceed with creation
        if (empty($errors)) {
            try {
                // Begin transaction
                $conexion->beginTransaction();

                // Create the invoice
                $factura = new Factura();
                $factura->setId_proveedor($id_proveedor);
                $factura->setFecha($fecha);
                $factura->setValor_total(0); // Will be calculated from details

                $factura_id = $factura->crear($conexion);
                if (!$factura_id) {
                    throw new Exception('Error al crear la factura.');
                }

                // Create invoice details and calculate total
                $valor_total = 0;
                foreach ($detalles_data as $detalle_data) {
                    $detalle = new FacturaDetalle();
                    $detalle->setId_factura($factura_id);
                    $detalle->setDescripcion($detalle_data['descripcion']);
                    $detalle->setCantidad((int)$detalle_data['cantidad']);
                    $detalle->setValor_unitario((float)$detalle_data['valor_unitario']);
                    
                    // Calculate detail total
                    $detalle_total = $detalle->getCantidad() * $detalle->getValor_unitario();
                    $detalle->setValor_total($detalle_total);
                    $valor_total += $detalle_total;

                    $detalle_id = $detalle->crear($conexion);
                    if (!$detalle_id) {
                        throw new Exception('Error al crear el detalle de la factura.');
                    }
                }

                // Update invoice total
                $factura->setId($factura_id);
                $factura->setValor_total($valor_total);
                if (!$factura->modificar($conexion)) {
                    throw new Exception('Error al actualizar el valor total de la factura.');
                }

                // Handle file uploads
                if (!empty($uploaded_files)) {
                    // Create upload directory
                    $upload_dir = __ROOT__ . "/resources/uploads/facturas/$factura_id/";
                    if (!is_dir($upload_dir)) {
                        if (!mkdir($upload_dir, 0755, true)) {
                            throw new Exception('Error al crear el directorio de archivos.');
                        }
                    }

                    // Move files and create support records
                    foreach ($uploaded_files as $file) {
                        $safe_filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $file['name']);
                        $destination = $upload_dir . $safe_filename;

                        if (move_uploaded_file($file['tmp_name'], $destination)) {
                            $soporte = new FacturaSoporte();
                            $soporte->setId_factura($factura_id);
                            $soporte->setNombre_archivo($safe_filename);

                            $soporte_id = $soporte->crear($conexion);
                            if (!$soporte_id) {
                                throw new Exception('Error al registrar el archivo de soporte.');
                            }
                        } else {
                            throw new Exception("Error al subir el archivo '{$file['name']}'.");
                        }
                    }
                }

                // Commit transaction
                $conexion->commit();

                // Redirect with success message
                $_SESSION['flash_message_success'] = 'Factura creada correctamente.';
                header('Location: ' . RUTA . 'admin/listado-facturas');
                exit;

            } catch (Exception $e) {
                // Rollback transaction
                $conexion->rollBack();
                $errors[] = 'Error al crear la factura: ' . $e->getMessage();
            }
        }

        // Store form data for redisplay
        $formData['id_proveedor'] = $id_proveedor;
        $formData['fecha'] = $fecha;
        $formData['detalles'] = $detalles_data ?: [];
    }
    #endregion Handle form submission

} catch (Exception $e) {
    error_log("Error in ifactura.php: " . $e->getMessage());
    $errors[] = 'Error del sistema: ' . $e->getMessage();
}

// Include the view
require_once __ROOT__ . '/views/admin/ifactura.view.php';
