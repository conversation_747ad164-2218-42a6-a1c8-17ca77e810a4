<?php
#region region DOCS

/** @var Proveedor[] $proveedores */
/** @var array $errors */
/** @var array $formData */

use App\classes\Proveedor;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Nueva Factura</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="Crear nueva factura" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
    <link href="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- BEGIN #header -->
    <?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>
    <!-- END #header -->

    <!-- BEGIN #sidebar -->
    <?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>
    <!-- END #sidebar -->

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Crear Nueva Factura</h4>
                <p class="mb-0 text-muted">Ingrese los detalles de la nueva factura con sus documentos de soporte</p>
            </div>
            <div class="ms-auto">
                <a href="listado-facturas" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver al Listado</a>
            </div>
        </div>
        <hr>
        <?php #endregion PAGE HEADER ?>

        <?php #region region ERROR MESSAGES ?>
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <h6><i class="fa fa-exclamation-triangle me-1"></i> Se encontraron los siguientes errores:</h6>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        <?php #endregion ERROR MESSAGES ?>

        <form action="ingresar-factura" method="POST" id="create-factura-form" enctype="multipart/form-data" novalidate>
            
            <?php #region region HEADER SECTION ?>
            <div class="panel panel-inverse no-border-radious mb-3">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">Información de la Factura</h4>
                    <div class="panel-heading-btn">
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="id_proveedor" class="form-label">Proveedor <span class="text-danger">*</span></label>
                            <select class="form-control" id="id_proveedor" name="id_proveedor" required>
                                <option value="">Seleccione un proveedor</option>
                                <?php foreach ($proveedores as $proveedor): ?>
                                    <option value="<?php echo $proveedor->getId(); ?>" 
                                            <?php echo ($formData['id_proveedor'] == $proveedor->getId()) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($proveedor->getNombre()); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">El proveedor es requerido.</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="fecha" class="form-label">Fecha <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha" name="fecha" 
                                       value="<?php echo htmlspecialchars($formData['fecha']); ?>" 
                                       required autocomplete="off" placeholder="yyyy-mm-dd">
                                <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                            </div>
                            <div class="invalid-feedback">La fecha es requerida.</div>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion HEADER SECTION ?>

            <?php #region region DETAILS SECTION ?>
            <div class="panel panel-inverse no-border-radious mb-3">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">Detalles de la Factura</h4>
                    <div class="panel-heading-btn">
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="mb-3">
                        <button type="button" class="btn btn-success btn-sm" id="add-detalle-btn">
                            <i class="fa fa-plus fa-fw me-1"></i> Agregar Detalle
                        </button>
                    </div>
                    
                    <div id="detalles-container">
                        <!-- Details will be added here dynamically -->
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6 offset-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <strong>Valor Total:</strong>
                                        <strong id="valor-total-display">$0 COP</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Hidden input to store details data -->
                    <input type="hidden" id="detalles-data" name="detalles_data">
                </div>
            </div>
            <?php #endregion DETAILS SECTION ?>

            <?php #region region SUPPORT DOCUMENTS SECTION ?>
            <div class="panel panel-inverse no-border-radious mb-3">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">Documentos de Soporte</h4>
                    <div class="panel-heading-btn">
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="mb-3">
                        <label for="archivos" class="form-label">Archivos PDF</label>
                        <input type="file" class="form-control" id="archivos" name="archivos[]" 
                               accept=".pdf" multiple>
                        <div class="form-text">
                            <i class="fa fa-info-circle me-1"></i>
                            Solo se permiten archivos PDF. Tamaño máximo: 10MB por archivo.
                        </div>
                    </div>
                    
                    <div id="files-preview" class="mt-3">
                        <!-- File preview will be shown here -->
                    </div>
                </div>
            </div>
            <?php #endregion SUPPORT DOCUMENTS SECTION ?>

            <?php #region region FORM ACTIONS ?>
            <div class="panel panel-inverse no-border-radious">
                <div class="panel-body text-end">
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fa fa-save fa-fw me-1"></i> Crear Factura
                    </button>
                    <a href="listado-facturas" class="btn btn-secondary btn-lg">
                        <i class="fa fa-times fa-fw me-1"></i> Cancelar
                    </a>
                </div>
            </div>
            <?php #endregion FORM ACTIONS ?>

        </form>

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region DETAIL ROW TEMPLATE ?>
<template id="detalle-row-template">
    <div class="detalle-row border rounded p-3 mb-3">
        <div class="row">
            <div class="col-md-4 mb-3">
                <label class="form-label">Descripción <span class="text-danger">*</span></label>
                <input type="text" class="form-control detalle-descripcion" placeholder="Descripción del producto/servicio" required>
                <div class="invalid-feedback">La descripción es requerida.</div>
            </div>
            <div class="col-md-2 mb-3">
                <label class="form-label">Cantidad <span class="text-danger">*</span></label>
                <input type="number" class="form-control detalle-cantidad" min="1" value="1" required>
                <div class="invalid-feedback">La cantidad debe ser mayor que cero.</div>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">Valor Unitario <span class="text-danger">*</span></label>
                <input type="text" class="form-control detalle-valor-unitario-display" placeholder="$0" required>
                <input type="hidden" class="detalle-valor-unitario">
                <div class="invalid-feedback">El valor unitario es requerido.</div>
            </div>
            <div class="col-md-2 mb-3">
                <label class="form-label">Valor Total</label>
                <input type="text" class="form-control detalle-valor-total" readonly>
            </div>
            <div class="col-md-1 mb-3 d-flex align-items-end">
                <button type="button" class="btn btn-danger btn-sm remove-detalle-btn" title="Eliminar detalle">
                    <i class="fa fa-trash-alt"></i>
                </button>
            </div>
        </div>
    </div>
</template>
<?php #endregion DETAIL ROW TEMPLATE ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
<script src="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA_RESOURCES ?>js/datepickerini.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('create-factura-form');
    const detallesContainer = document.getElementById('detalles-container');
    const addDetalleBtn = document.getElementById('add-detalle-btn');
    const valorTotalDisplay = document.getElementById('valor-total-display');
    const detallesDataInput = document.getElementById('detalles-data');
    const filesInput = document.getElementById('archivos');
    const filesPreview = document.getElementById('files-preview');

    let detalleCounter = 0;

    // Add initial detail row
    addDetalleRow();

    // Add detail row functionality
    addDetalleBtn.addEventListener('click', function() {
        addDetalleRow();
    });

    // File preview functionality
    filesInput.addEventListener('change', function() {
        showFilesPreview();
    });

    // Form submission
    form.addEventListener('submit', function(event) {
        if (!validateForm()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            // Prepare details data
            prepareDetallesData();
        }
    });

    function addDetalleRow() {
        const template = document.getElementById('detalle-row-template');
        const clone = template.content.cloneNode(true);

        // Set unique IDs and names
        const row = clone.querySelector('.detalle-row');
        row.dataset.index = detalleCounter;

        // Add event listeners
        const removeBtn = clone.querySelector('.remove-detalle-btn');
        removeBtn.addEventListener('click', function() {
            removeDetalleRow(row);
        });

        // Currency formatting for valor unitario
        const valorUnitarioDisplay = clone.querySelector('.detalle-valor-unitario-display');
        const valorUnitarioHidden = clone.querySelector('.detalle-valor-unitario');
        const cantidadInput = clone.querySelector('.detalle-cantidad');
        const valorTotalInput = clone.querySelector('.detalle-valor-total');

        valorUnitarioDisplay.addEventListener('input', function() {
            // Format currency and update hidden field
            let rawValue = this.value.replace(/\D/g, '');
            valorUnitarioHidden.value = rawValue;

            if (rawValue) {
                let numberValue = parseInt(rawValue, 10);
                this.value = '$' + new Intl.NumberFormat('es-CO', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(numberValue);
            }

            calculateDetalleTotal(row);
        });

        cantidadInput.addEventListener('input', function() {
            calculateDetalleTotal(row);
        });

        detallesContainer.appendChild(clone);
        detalleCounter++;

        updateRemoveButtons();
    }

    function removeDetalleRow(row) {
        row.remove();
        updateRemoveButtons();
        calculateGrandTotal();
    }

    function updateRemoveButtons() {
        const rows = detallesContainer.querySelectorAll('.detalle-row');
        rows.forEach(row => {
            const removeBtn = row.querySelector('.remove-detalle-btn');
            removeBtn.style.display = rows.length > 1 ? 'block' : 'none';
        });
    }

    function calculateDetalleTotal(row) {
        const cantidadInput = row.querySelector('.detalle-cantidad');
        const valorUnitarioHidden = row.querySelector('.detalle-valor-unitario');
        const valorTotalInput = row.querySelector('.detalle-valor-total');

        const cantidad = parseInt(cantidadInput.value) || 0;
        const valorUnitario = parseInt(valorUnitarioHidden.value) || 0;
        const total = cantidad * valorUnitario;

        valorTotalInput.value = '$' + new Intl.NumberFormat('es-CO', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(total);

        calculateGrandTotal();
    }

    function calculateGrandTotal() {
        let grandTotal = 0;
        const rows = detallesContainer.querySelectorAll('.detalle-row');

        rows.forEach(row => {
            const cantidadInput = row.querySelector('.detalle-cantidad');
            const valorUnitarioHidden = row.querySelector('.detalle-valor-unitario');

            const cantidad = parseInt(cantidadInput.value) || 0;
            const valorUnitario = parseInt(valorUnitarioHidden.value) || 0;
            grandTotal += cantidad * valorUnitario;
        });

        valorTotalDisplay.textContent = '$' + new Intl.NumberFormat('es-CO', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(grandTotal) + ' COP';
    }

    function showFilesPreview() {
        const files = filesInput.files;
        let html = '';

        if (files.length > 0) {
            html = '<h6>Archivos seleccionados:</h6><div class="list-group">';

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const isValid = file.type === 'application/pdf' && file.size <= 10 * 1024 * 1024;
                const statusClass = isValid ? 'list-group-item-success' : 'list-group-item-danger';
                const statusIcon = isValid ? 'fa-check-circle text-success' : 'fa-exclamation-triangle text-danger';
                const statusText = isValid ? 'Válido' : 'Inválido (debe ser PDF y menor a 10MB)';

                html += `
                    <div class="list-group-item ${statusClass}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fa fa-file-pdf me-2"></i>
                                ${file.name}
                                <small class="text-muted">(${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
                            </div>
                            <div>
                                <i class="fa ${statusIcon} me-1"></i>
                                ${statusText}
                            </div>
                        </div>
                    </div>
                `;
            }

            html += '</div>';
        }

        filesPreview.innerHTML = html;
    }

    function prepareDetallesData() {
        const rows = detallesContainer.querySelectorAll('.detalle-row');
        const detalles = [];

        rows.forEach(row => {
            const descripcion = row.querySelector('.detalle-descripcion').value.trim();
            const cantidad = parseInt(row.querySelector('.detalle-cantidad').value) || 0;
            const valorUnitario = parseInt(row.querySelector('.detalle-valor-unitario').value) || 0;

            if (descripcion && cantidad > 0 && valorUnitario > 0) {
                detalles.push({
                    descripcion: descripcion,
                    cantidad: cantidad,
                    valor_unitario: valorUnitario
                });
            }
        });

        detallesDataInput.value = JSON.stringify(detalles);
    }

    function validateForm() {
        let isValid = true;

        // Reset validation states
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));

        // Validate proveedor
        const proveedorSelect = document.getElementById('id_proveedor');
        if (!proveedorSelect.value) {
            proveedorSelect.classList.add('is-invalid');
            isValid = false;
        }

        // Validate fecha
        const fechaInput = document.getElementById('fecha');
        if (!fechaInput.value.trim()) {
            fechaInput.classList.add('is-invalid');
            isValid = false;
        }

        // Validate details
        const rows = detallesContainer.querySelectorAll('.detalle-row');
        let hasValidDetail = false;

        rows.forEach(row => {
            const descripcion = row.querySelector('.detalle-descripcion');
            const cantidad = row.querySelector('.detalle-cantidad');
            const valorUnitario = row.querySelector('.detalle-valor-unitario');

            let rowValid = true;

            if (!descripcion.value.trim()) {
                descripcion.classList.add('is-invalid');
                rowValid = false;
                isValid = false;
            }

            if (!cantidad.value || parseInt(cantidad.value) <= 0) {
                cantidad.classList.add('is-invalid');
                rowValid = false;
                isValid = false;
            }

            if (!valorUnitario.value || parseInt(valorUnitario.value) <= 0) {
                row.querySelector('.detalle-valor-unitario-display').classList.add('is-invalid');
                rowValid = false;
                isValid = false;
            }

            if (rowValid) {
                hasValidDetail = true;
            }
        });

        if (!hasValidDetail) {
            isValid = false;
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Debe agregar al menos un detalle válido a la factura.'
            });
        }

        // Validate files if any
        const files = filesInput.files;
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            if (file.type !== 'application/pdf') {
                isValid = false;
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: `El archivo '${file.name}' debe ser un PDF.`
                });
                break;
            }
            if (file.size > 10 * 1024 * 1024) {
                isValid = false;
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: `El archivo '${file.name}' es demasiado grande (máximo 10MB).`
                });
                break;
            }
        }

        if (!isValid) {
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.focus();
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        return isValid;
    }

    // Load existing data if any (for form redisplay after errors)
    <?php if (!empty($formData['detalles'])): ?>
    // Clear the initial empty row
    detallesContainer.innerHTML = '';
    detalleCounter = 0;

    // Add rows with existing data
    <?php foreach ($formData['detalles'] as $index => $detalle): ?>
    addDetalleRow();
    const row<?php echo $index; ?> = detallesContainer.querySelector('.detalle-row:last-child');
    row<?php echo $index; ?>.querySelector('.detalle-descripcion').value = <?php echo json_encode($detalle['descripcion']); ?>;
    row<?php echo $index; ?>.querySelector('.detalle-cantidad').value = <?php echo (int)$detalle['cantidad']; ?>;

    const valorUnitario<?php echo $index; ?> = <?php echo (int)$detalle['valor_unitario']; ?>;
    row<?php echo $index; ?>.querySelector('.detalle-valor-unitario').value = valorUnitario<?php echo $index; ?>;
    row<?php echo $index; ?>.querySelector('.detalle-valor-unitario-display').value = '$' + new Intl.NumberFormat('es-CO').format(valorUnitario<?php echo $index; ?>);

    calculateDetalleTotal(row<?php echo $index; ?>);
    <?php endforeach; ?>

    updateRemoveButtons();
    <?php endif; ?>
});
</script>
<?php #endregion JS ?>
</body>
</html>
